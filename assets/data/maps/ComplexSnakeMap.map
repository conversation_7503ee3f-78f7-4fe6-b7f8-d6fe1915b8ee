# Tower Defense Map File - Complex Snake Maze
# A larger, more intricate "snake" style map that winds through the playable area.

[Metadata]
# Map name
name = Snake Maze

# Map width (tiles)
width = 20

# Map height (tiles)
height = 15

# Size of each tile in the game world
# (Should match your engine settings)
tileSize = 64.0

# Initial game settings (optional)
initialLives = 20
initialGold = 500

# [Tile Legend]
# 'G' : BUILDABLE_GROUND  – Remote-tower buildable ground (not on path, adjacent to path)
# 'P' : PATH              – Common path tile (melee towers deployable here)
# 'S' : START_POINT       – Enemy spawn point (also a path tile, melee towers deployable)
# 'E' : END_POINT         – Our base / goal tile (path end)
# 'W' : OBSTACLE          – Impassable wall or obstacle (no movement, no building)

[Tiles]
# Map layout (width × height)
WWWWWWWWWWWWWWWWWWWW
WGGGGGGGGGGGGGGGGGGW
WGSPPPPPPPPPPPPPPGW
WGWWWWWWWWWWWWWWWGPW
WGPPPPPPPPPPPPPPPPGW
WGPGWWWWWWWWWWGPGW
WGPPPPPPPPPPPPPPPPGW
WGWWWWWWWWWWWWWWWGPW
WGPPPPPPPPPPPPPPPPGW
WGPGWWWWWWWWWWGPGW
WGPPPPPPPPPPPPPPPPGW
WGWWWWWWWWWWWWWWWGPW
WGPPPPPPPPPPPPPPPPGW
WGEGWWWWWWWWWWWWWGWW
WWWWWWWWWWWWWWWWWWWW

[Paths]
# 敌人行进路径 - 单条 1 格宽蛇形通道
Path:0
2,2   # S - start
3,2
4,2
5,2
6,2
7,2
8,2
9,2
10,2
11,2
12,2
13,2
14,2
15,2
16,2
17,2
17,3
17,4
16,4
15,4
14,4
13,4
12,4
11,4
10,4
9,4
8,4
7,4
6,4
5,4
4,4
3,4
2,4
2,5
2,6
3,6
4,6
5,6
6,6
7,6
8,6
9,6
10,6
11,6
12,6
13,6
14,6
15,6
16,6
17,6
17,7
17,8
16,8
15,8
14,8
13,8
12,8
11,8
10,8
9,8
8,8
7,8
6,8
5,8
4,8
3,8
2,8
2,9
2,10
3,10
4,10
5,10
6,10
7,10
8,10
9,10
10,10
11,10
12,10
13,10
14,10
15,10
16,10
17,10
17,11
17,12
16,12
15,12
14,12
13,12
12,12
11,12
10,12
9,12
8,12
7,12
6,12
5,12
4,12
3,12
2,12
2,13   # E - end